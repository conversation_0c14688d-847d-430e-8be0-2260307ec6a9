<?php

namespace Database\Factories;

use App\Models\Webinar;
use Illuminate\Database\Eloquent\Factories\Factory;

class WebinarFactory extends Factory
{
    protected $model = Webinar::class;

    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'title' => $this->faker->sentence(3),
            'speaker' => $this->faker->name,
            'virtual_viewers' => $this->faker->numberBetween(10, 1000),
            'waiting_time' => $this->faker->numberBetween(10, 60),
            'join_code' => strtoupper($this->faker->unique()->bothify('??##??##')),
            'join_url' => $this->faker->url,
            'join_count' => $this->faker->numberBetween(1, 10),
            'join_settings' => json_encode(['auto_approve' => $this->faker->boolean]),
            'notification_settings' => json_encode(['reminder' => $this->faker->boolean]),
            'video_path' => $this->faker->optional()->url,
            's3_url' => $this->faker->optional()->url,
            'schedules' => json_encode([['date' => $this->faker->dateTimeThisMonth()->format('Y-m-d H:i:s')]]),
            'seeded_comments' => json_encode([$this->faker->sentence]),
            'advertisement_slots' => json_encode([$this->faker->company]),
            'video_duration_minutes' => $this->faker->numberBetween(10, 180),
            'allow_replay' => $this->faker->boolean,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }
} 