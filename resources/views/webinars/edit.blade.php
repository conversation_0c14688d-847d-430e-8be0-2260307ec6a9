@extends('layouts.app')

@section('content')
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Chỉnh Sửa Webinar</h5>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ route('webinars.update', $webinar) }}">
                @csrf
                @method('PUT')
                <input type="hidden" name="active_tab" id="active_tab" value="general">

                <ul class="nav nav-tabs" id="webinarTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general"
                                type="button" role="tab" aria-controls="general" aria-selected="true">
                            <i class="fas fa-cog"></i> Cài Đặt Chung
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="schedule-tab" data-bs-toggle="tab" data-bs-target="#schedule"
                                type="button" role="tab" aria-controls="schedule" aria-selected="false">
                            <i class="fas fa-calendar-alt"></i> Lịch Trình
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="join-settings-tab" data-bs-toggle="tab"
                                data-bs-target="#join-settings" type="button" role="tab" aria-controls="join-settings"
                                aria-selected="false">
                            <i class="fas fa-user-plus"></i> Form Đăng Ký
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notification-settings-tab" data-bs-toggle="tab"
                                data-bs-target="#notification-settings" type="button" role="tab"
                                aria-controls="notification-settings" aria-selected="false">
                            <i class="fas fa-bell"></i> Thông Báo
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="seeded-comments-tab" data-bs-toggle="tab"
                                data-bs-target="#seeded-comments" type="button" role="tab"
                                aria-controls="seeded-comments" aria-selected="false">
                            <i class="fas fa-comments"></i> Bình Luận
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="advertisements-tab" data-bs-toggle="tab"
                                data-bs-target="#advertisements" type="button" role="tab" aria-controls="advertisements"
                                aria-selected="false">
                            <i class="fas fa-ad"></i> Quảng Cáo
                        </button>
                    </li>
                </ul>

                <div class="tab-content" id="webinarTabsContent">
                    <!-- General Settings Tab -->
                    <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                        <div class="mb-3">
                            <label for="title" class="form-label">Tiêu Đề Webinar</label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" id="title"
                                   name="title" value="{{ old('title', $webinar->title) }}" required>
                            @error('title')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="speaker" class="form-label fw-bold">Người Thuyết Trình</label>
                            <input type="text" class="form-control @error('speaker') is-invalid @enderror" id="speaker"
                                   name="speaker" value="{{ old('speaker', $webinar->speaker) }}" required>
                            @error('speaker')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>

                        @if($webinar->video_path)
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>Lưu ý về phân tích:</strong> Hệ thống đang thu thập dữ liệu về thời gian xem và
                                loại thiết bị của người tham gia.
                                Dữ liệu này sẽ được cập nhật dần dần và hiển thị đầy đủ trong phần phân tích.
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="virtual_viewers" class="form-label">Số Người Xem Ảo</label>
                            <input type="text" class="form-control @error('virtual_viewers') is-invalid @enderror"
                                   id="virtual_viewers" name="virtual_viewers"
                                   value="{{ old('virtual_viewers', $webinar->virtual_viewers) }}" required>
                            <div class="form-text">Số lượng người xem ảo hiển thị (nhập một số từ 10-1000 hoặc khoảng,
                                ví dụ: 10-20)
                            </div>
                            @error('virtual_viewers')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                            <div class="invalid-feedback" id="virtual-viewers-error">
                                Vui lòng nhập một số nguyên từ 10 (ví dụ: 10-20).
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="allow_replay"
                                       name="allow_replay" {{ old('allow_replay', $webinar->allow_replay) ? 'checked' : '' }}>
                                <label class="form-check-label" for="allow_replay">
                                    <i class="fas fa-play-circle me-2 text-primary"></i> Cho phép xem lại video sau khi
                                    webinar kết thúc
                                </label>
                                <div class="form-text">Nếu tắt tùy chọn này, người tham gia không thể xem lại video sau
                                    khi webinar đã kết thúc.
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="only_show_my_comment"
                                       name="only_show_my_comment" {{ old('only_show_my_comment', $webinar->only_show_my_comment) ? 'checked' : '' }}>
                                <label class="form-check-label" for="only_show_my_comment">
                                    Chỉ cho phép xem bình luận của chính mình
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="waiting_time" class="form-label">Thời Gian Chờ (phút)</label>
                            <input type="number" class="form-control @error('waiting_time') is-invalid @enderror"
                                   id="waiting_time" name="waiting_time"
                                   value="{{ old('waiting_time', $webinar->waiting_time) }}" min="5" max="60" required>
                            <div class="form-text">Thời gian cho phép người tham gia vào phòng chờ trước khi webinar bắt
                                đầu (5-60 phút)
                            </div>
                            @error('waiting_time')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="join_url" class="form-label">Đường Dẫn Tham Gia</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="join_url" value="{{ $webinar->join_url }}"
                                       readonly>
                                <button class="btn btn-outline-secondary copy-btn" type="button"
                                        data-clipboard-target="#join_url" title="Sao chép">
                                    <i class="fas fa-copy"></i>
                                </button>
                                <button class="btn btn-outline-secondary" id="regenerate-url" type="button"
                                        title="Tạo đường dẫn mới">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>
                            <div class="form-text">Nhấn vào nút <i class="fas fa-sync-alt"></i> để tạo một đường dẫn
                                mới.
                            </div>
                        </div>
                    </div>

                    <!-- Schedule Tab -->
                    <div class="tab-pane fade" id="schedule" role="tabpanel" aria-labelledby="schedule-tab">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5>Lịch Trình Webinar</h5>
                            <button type="button" class="btn btn-sm btn-success" id="add-schedule">
                                <i class="fas fa-plus"></i> Thêm Ngày/Giờ
                            </button>
                        </div>

                        <div id="schedules-container">
                            <!-- Schedule items will be added here -->
                            @if($webinar->schedules && count($webinar->schedules) > 0)
                                @foreach($webinar->schedules as $index => $schedule)
                                    <div class="schedule-item mb-3 card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-5">
                                                    <label class="form-label">Ngày</label>
                                                    <input type="date" class="form-control"
                                                           name="schedules[{{ $index }}][date]"
                                                           value="{{ $schedule['date'] }}">
                                                </div>
                                                <div class="col-md-5">
                                                    <label class="form-label">Giờ</label>
                                                    <input type="time" class="form-control"
                                                           name="schedules[{{ $index }}][time]"
                                                           value="{{ $schedule['time'] }}">
                                                </div>
                                                <div class="col-md-2 d-flex align-items-end">
                                                    <button type="button"
                                                            class="btn btn-danger remove-schedule mt-2 w-100" {{ count($webinar->schedules) <= 1 ? 'disabled' : '' }}>
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="schedule-item mb-3 card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-5">
                                                <label class="form-label">Ngày</label>
                                                <input type="date" class="form-control" name="schedules[0][date]"
                                                       min="{{ date('Y-m-d') }}">
                                            </div>
                                            <div class="col-md-5">
                                                <label class="form-label">Giờ</label>
                                                <input type="time" class="form-control" name="schedules[0][time]">
                                            </div>
                                            <div class="col-md-2 d-flex align-items-end">
                                                <button type="button" class="btn btn-danger remove-schedule mt-2 w-100"
                                                        disabled>
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        @error('schedules')
                        <div class="alert alert-danger">
                            <strong>{{ $message }}</strong>
                        </div>
                        @enderror

                        @error('schedules.*')
                        <div class="alert alert-danger">
                            <strong>{{ $message }}</strong>
                        </div>
                        @enderror
                    </div>

                    <!-- Join Settings Tab -->
                    <div class="tab-pane fade" id="join-settings" role="tabpanel" aria-labelledby="join-settings-tab">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Cài đặt Form Đăng ký</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-4">
                                    Chọn các thông tin mà người tham gia cần phải cung cấp khi đăng ký tham gia webinar.
                                </p>

                                <div class="mb-3">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="name_required"
                                               name="name_required" {{ $webinar->join_settings['name_required'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="name_required">
                                            <i class="fas fa-user me-2 text-primary"></i> Họ và tên (bắt buộc)
                                        </label>
                                    </div>

                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox" id="phone_required"
                                               name="phone_required" {{ $webinar->join_settings['phone_required'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="phone_required">
                                            <i class="fas fa-phone me-2 text-primary"></i> Số điện thoại (bắt buộc)
                                        </label>
                                    </div>

                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="email_required"
                                               name="email_required" {{ $webinar->join_settings['email_required'] ? 'checked' : '' }}>
                                        <label class="form-check-label" for="email_required">
                                            <i class="fas fa-envelope me-2 text-primary"></i> Email (bắt buộc)
                                        </label>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Khi người tham gia nhấn vào liên kết tham
                                    gia, họ sẽ được yêu cầu điền thông tin tương ứng với các tùy chọn trên.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notification Settings Tab -->
                    <div class="tab-pane fade" id="notification-settings" role="tabpanel"
                         aria-labelledby="notification-settings-tab">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0"><i class="fab fa-telegram text-primary me-2"></i>Cài đặt thông báo
                                    Telegram</h5>
                            </div>
                            <div class="card-body">
                                <p class="text-muted mb-4">
                                    Cấu hình thông báo Telegram cho webinar này. Cài đặt này sẽ ghi đè lên cài đặt toàn
                                    cục.
                                </p>

                                <div class="mb-3">
                                    <div class="form-check form-switch mb-3">
                                        <input class="form-check-input" type="checkbox"
                                               id="telegram_notify_participants"
                                               name="telegram_notify_participants"
                                               value="1" {{ isset($webinar->notification_settings['telegram_notify_participants']) && $webinar->notification_settings['telegram_notify_participants'] == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="telegram_notify_participants">
                                            <i class="fas fa-user-plus me-2 text-primary"></i> Thông báo khi có người
                                            tham gia webinar
                                        </label>
                                    </div>

                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="telegram_notify_comments"
                                               name="telegram_notify_comments"
                                               value="1" {{ isset($webinar->notification_settings['telegram_notify_comments']) && $webinar->notification_settings['telegram_notify_comments'] == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="telegram_notify_comments">
                                            <i class="fas fa-comment me-2 text-primary"></i> Thông báo khi có bình luận
                                            mới
                                        </label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="telegram_notify_order_new"
                                               name="telegram_notify_order_new"
                                               value="1" {{ isset($webinar->notification_settings['telegram_notify_order_new']) && $webinar->notification_settings['telegram_notify_order_new'] == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="telegram_notify_order_new">

                                            <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>Thông báo khi có
                                            đơn hàng mới
                                        </label>
                                    </div>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox"
                                               id="telegram_notify_order_payment_success"
                                               name="telegram_notify_order_payment_success"
                                               value="1" {{ isset($webinar->notification_settings['telegram_notify_order_payment_success']) && $webinar->notification_settings['telegram_notify_order_payment_success'] == '1' ? 'checked' : '' }}>
                                        <label class="form-check-label" for="telegram_notify_order_payment_success">
                                            <i class="fas fa-money-bill-transfer me-2 text-primary"></i>Thông báo khi
                                            đơn chuyển khoản tự động thành công
                                        </label>
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> Đảm bảo đã thiết lập Telegram Bot trong <a
                                        href="{{ route('settings.index', ['group' => 'notifications']) }}"
                                        target="_blank">cài đặt hệ thống</a> trước khi sử dụng tính năng này.
                                    <hr>

                                    <small>
                                        <i class="fas fa-check-circle me-1"></i> Thông báo Telegram được xử lý qua hàng
                                        đợi (queue) để không làm chậm trải nghiệm người dùng.<br>
                                        <i class="fab fa-telegram me-1"></i> Chat với bot tại: <a
                                            href="https://t.me/{{ $telegramSettings['telegram_bot_username'] ?? '' }}"
                                            target="_blank">https://t.me/{{ $telegramSettings['telegram_bot_username'] ?? '' }}</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Seeded Comments Tab -->
                    <div class="tab-pane fade" id="seeded-comments" role="tabpanel"
                         aria-labelledby="seeded-comments-tab">
                        <div class="card-body">
                            <div class="alert alert-info mb-3">
                                <h5><i class="fas fa-info-circle me-2"></i> Bình luận seeding</h5>
                                <p class="mb-0">Bình luận seeding sẽ tự động xuất hiện tại thời điểm trong video xác
                                    định bởi trường "Thời gian". Thời gian cần theo định dạng HH:MM:SS (ví dụ:
                                    00:01:30).</p>
                                <p class="mb-0 mt-1">Khi người xem đạt đến thời điểm đó trong video, bình luận sẽ hiển
                                    thị tự động, tạo cảm giác tương tác thực.</p>
                            </div>

                            <div id="comments-container">
                                @if($webinar->seeded_comments && count($webinar->seeded_comments) > 0)
                                    @foreach($webinar->seeded_comments as $index => $comment)
                                        <div class="comment-item mb-2 d-flex">
                                            <input type="text" class="form-control form-control-sm me-2"
                                                   name="seeded_comments[{{ $index }}][time]"
                                                   placeholder="00:00:00"
                                                   value="{{ $comment['time'] ?? '' }}"
                                                   pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}">

                                            <input type="text" class="form-control form-control-sm me-2"
                                                   name="seeded_comments[{{ $index }}][name]"
                                                   placeholder="Tên người bình luận"
                                                   value="{{ $comment['name'] ?? '' }}">

                                            <input type="text" class="form-control form-control-sm me-2"
                                                   name="seeded_comments[{{ $index }}][content]"
                                                   placeholder="Nội dung bình luận"
                                                   value="{{ $comment['content'] ?? '' }}">

                                            <button type="button" class="btn btn-sm btn-danger delete-comment-btn">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="comment-item mb-2 d-flex">
                                        <input type="text" class="form-control form-control-sm me-2"
                                               name="seeded_comments[0][time]"
                                               placeholder="00:00:00"
                                               value="00:00:10">

                                        <input type="text" class="form-control form-control-sm me-2"
                                               name="seeded_comments[0][name]"
                                               placeholder="Tên người bình luận"
                                               value="Đạt Tôi">

                                        <input type="text" class="form-control form-control-sm me-2"
                                               name="seeded_comments[0][content]"
                                               placeholder="Nội dung bình luận"
                                               value="cần mua áo">

                                        <button type="button" class="btn btn-sm btn-danger delete-comment-btn">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                @endif
                            </div>

                            <div class="mt-3 d-flex justify-content-end">
                                <button type="button" class="btn btn-sm btn-success" id="add-comment-btn">
                                    <i class="fas fa-plus me-1"></i> Thêm bình luận
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i> Bạn có thể tạo nhiều bình luận seeding để tạo không
                            khí tương tác cho webinar của mình.
                            <hr>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> Định dạng thời gian: HH:MM:SS (ví dụ: 00:15:30 cho 15
                                phút 30 giây)
                            </small>
                        </div>
                        <div>
                            <label class="form-label">
                                Ghim bình luận
                            </label>
                            <textarea class="form-control form-control-sm me-2"
                                      name="pin_comment"
                                      placeholder="Nội dung bình luận được ghim trên đầu">{{old("pin_comment",$webinar->pin_comment)}}</textarea>
                        </div>

                        <div class="d-flex justify-content-between mt-3">
                            <div>
                                <button type="button" class="btn btn-outline-primary" id="upload-excel-btn">
                                    <i class="fas fa-file-excel me-1"></i> Upload Excel
                                </button>
                                <a href="{{ route('comments.template') }}" class="btn btn-outline-info ms-2">
                                    <i class="fas fa-download me-1"></i> Tải file mẫu
                                </a>
                            </div>
                            <button type="button" class="btn btn-outline-success" id="generate-comments-btn">
                                <i class="fas fa-magic me-1"></i> Tạo tự động
                            </button>
                        </div>
                    </div>

                    <!-- Advertisements Tab -->
                    <div class="tab-pane fade" id="advertisements" role="tabpanel" aria-labelledby="advertisements-tab">
                        <div class="mb-3">
                            <h5 class="mb-3">
                                <i class="fas fa-ad text-primary me-2"></i> Cấu hình quảng cáo trong webinar
                            </h5>
                        </div>

                        <p class="text-muted mb-3">
                            Thêm quảng cáo sẽ hiển thị tại các thời điểm cụ thể trong quá trình phát webinar.
                        </p>

                        <div class="mb-3">
                            <div class="d-flex fw-bold mb-2">
                                <div>Thời gian | Quảng cáo</div>
                            </div>

                            <div id="advertisement-slots-container">
                                @if($webinar->advertisement_slots && count($webinar->advertisement_slots) > 0)
                                    @foreach($webinar->advertisement_slots as $index => $slot)
                                        <div class="advertisement-slot mb-3 card">
                                            <div class="card-body">
                                                <div class="row align-items-end">
                                                    <div class="col-md-3">
                                                        <label class="form-label">Thời gian hiển thị</label>
                                                        <input type="text" class="form-control"
                                                               name="advertisement_slots[{{ $index }}][time]"
                                                               placeholder="00:00:00"
                                                               value="{{ $slot['time'] ?? '00:00:10' }}"
                                                               pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}">
                                                    </div>
                                                    <div class="col-md-7">
                                                        <label class="form-label">Chọn quảng cáo</label>
                                                        <select class="form-select"
                                                                name="advertisement_slots[{{ $index }}][advertisement_id]">
                                                            <option value="">-- Chọn quảng cáo --</option>
                                                            @foreach($advertisements as $ad)
                                                                <option
                                                                    value="{{ $ad->id }}" {{ ($slot['advertisement_id'] ?? '') == $ad->id ? 'selected' : '' }}>
                                                                    {{ $ad->name ?? ($ad->type == 'image' ? 'Hình ảnh' : 'Sản phẩm') }}
                                                                </option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-md-2">
                                                        <button type="button"
                                                                class="btn btn-danger remove-ad-slot w-100">
                                                            <i class="fas fa-trash"></i> Xóa
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="advertisement-slot mb-3 card">
                                        <div class="card-body">
                                            <div class="row align-items-end">
                                                <div class="col-md-3">
                                                    <label class="form-label">Thời gian hiển thị</label>
                                                    <input type="text" class="form-control"
                                                           name="advertisement_slots[0][time]"
                                                           placeholder="00:00:00"
                                                           value="{{ $slot['time'] ?? '00:00:10' }}"
                                                           pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}">

                                                </div>
                                                <div class="col-md-7">
                                                    <label class="form-label">Chọn quảng cáo</label>
                                                    <select class="form-select"
                                                            name="advertisement_slots[0][advertisement_id]">
                                                        <option value="">-- Chọn quảng cáo --</option>
                                                        @foreach($advertisements as $ad)
                                                            <option
                                                                value="{{ $ad->id }}" {{ ($slot['advertisement_id'] ?? '') == $ad->id ? 'selected' : '' }}>
                                                                {{ $ad->name ?? ($ad->type == 'image' ? 'Hình ảnh' : 'Sản phẩm') }}
                                                                : {{ $ad->product_id ? $ad->product->name : $ad->url }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-2">
                                                    <button type="button" class="btn btn-danger remove-ad-slot w-100">
                                                        <i class="fas fa-trash"></i> Xóa
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <div class="mt-3 d-flex justify-content-end">
                                <button type="button" class="btn btn-sm btn-success" id="add-ad-slot-btn">
                                    <i class="fas fa-plus me-1"></i> Thêm quảng cáo
                                </button>
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i> Quảng cáo sẽ hiển thị tại thời điểm được chỉ định
                            trong video.
                            <hr>
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i> Định dạng thời gian: HH:MM:SS (ví dụ: 00:15:30 cho 15
                                phút 30 giây)
                            </small>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between mt-4">
                    <a href="{{ route('webinars.show', $webinar) }}" class="btn btn-secondary">Hủy</a>
                    <button type="submit" class="btn btn-primary">Cập Nhật Webinar</button>
                </div>
            </form>
        </div>
    </div>
@endsection

@push('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Kiểm tra URL parameters hoặc session để xác định tab nào cần active
            @if(session('tab'))
            document.getElementById('{{ session('tab') }}-tab').click();
            @endif

            const urlParams = new URLSearchParams(window.location.search);
            if (urlParams.has('tab')) {
                const tab = urlParams.get('tab');
                document.getElementById(`${tab}-tab`).click();
            }

            // Check if URL has hash for tab
            if (window.location.hash) {
                const hash = window.location.hash.substring(1);
                document.getElementById(`${hash}-tab`).click();
            }

            // Cập nhật active_tab khi chuyển tab
            const tabLinks = document.querySelectorAll('button[data-bs-toggle="tab"]');
            tabLinks.forEach(tabLink => {
                tabLink.addEventListener('shown.bs.tab', function (event) {
                    const targetId = event.target.getAttribute('id');
                    let tabName = 'general';

                    if (targetId === 'schedule-tab') {
                        tabName = 'schedule';
                    } else if (targetId === 'join-settings-tab') {
                        tabName = 'join-settings';
                    } else if (targetId === 'notification-settings-tab') {
                        tabName = 'notification-settings';
                    } else if (targetId === 'seeded-comments-tab') {
                        tabName = 'seeded-comments';
                    } else if (targetId === 'advertisements-tab') {
                        tabName = 'advertisements';
                    }

                    document.getElementById('active_tab').value = tabName;
                });
            });

            // Form validation
            const webinarForm = document.querySelector('form');

            webinarForm.addEventListener('submit', function (e) {
                let hasErrors = false;
                let errorMessages = [];

                // Kiểm tra trường số người xem ảo
                const virtualViewersInput = document.getElementById('virtual_viewers');
                const virtualViewersValue = virtualViewersInput.value;

                if (!isValidViewerInput(virtualViewersValue)) {
                    hasErrors = true;
                    errorMessages.push('Số người xem ảo: Vui lòng nhập một số từ 10-1000 hoặc khoảng hợp lệ (ví dụ: 10-20).');
                    virtualViewersInput.classList.add('is-invalid');
                }

                // Kiểm tra các trường trong lịch trình
                const scheduleItems = document.querySelectorAll('.schedule-item');
                if (scheduleItems.length === 0) {
                    hasErrors = true;
                    errorMessages.push('Vui lòng thêm ít nhất một lịch trình.');
                }

                scheduleItems.forEach((item, index) => {
                    const dateInput = item.querySelector('input[name^="schedules"][name$="[date]"]');
                    const timeInput = item.querySelector('input[name^="schedules"][name$="[time]"]');

                    if (!dateInput.value) {
                        hasErrors = true;
                        errorMessages.push(`Lịch trình #${index + 1}: Vui lòng nhập ngày.`);
                        dateInput.classList.add('is-invalid');
                    }

                    if (!timeInput.value) {
                        hasErrors = true;
                        errorMessages.push(`Lịch trình #${index + 1}: Vui lòng nhập giờ.`);
                        timeInput.classList.add('is-invalid');
                    } else if (!/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(timeInput.value)) {
                        hasErrors = true;
                        errorMessages.push(`Lịch trình #${index + 1}: Giờ không đúng định dạng (HH:MM).`);
                        timeInput.classList.add('is-invalid');
                    }
                });

                // Nếu có lỗi, hiển thị thông báo và ngăn form submit
                if (hasErrors) {
                    e.preventDefault();

                    // Kiểm tra nếu lỗi liên quan đến lịch trình
                    let hasScheduleError = false;
                    for (const msg of errorMessages) {
                        if (msg.includes('Lịch trình')) {
                            hasScheduleError = true;
                            break;
                        }
                    }

                    // Nếu lỗi liên quan đến lịch trình, hiển thị tab lịch trình
                    if (hasScheduleError) {
                        document.getElementById('schedule-tab').click();
                    }

                    // Hiển thị thông báo lỗi với SweetAlert2
                    let errorContent = '<ul class="text-start mb-0">';
                    errorMessages.forEach(msg => {
                        errorContent += `<li>${msg}</li>`;
                    });
                    errorContent += '</ul>';

                    Swal.fire({
                        title: 'Lỗi Dữ Liệu',
                        html: errorContent,
                        icon: 'error',
                        confirmButtonText: 'Đã hiểu',
                        confirmButtonColor: '#4a6cf7'
                    });
                } else {
                    // Hiển thị thông báo đang xử lý
                    Swal.fire({
                        title: 'Đang xử lý...',
                        html: 'Vui lòng đợi trong giây lát',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                }
            });

            // Xử lý khi người dùng thay đổi giá trị trường input
            document.querySelectorAll('input, select, textarea').forEach(input => {
                input.addEventListener('input', function () {
                    this.classList.remove('is-invalid');
                });
            });

            // Initialize clipboard
            var clipboard = new ClipboardJS('.copy-btn');
            clipboard.on('success', function (e) {
                let button = e.trigger;
                let originalHTML = button.innerHTML;

                button.innerHTML = '<i class="fas fa-check"></i>';
                setTimeout(function () {
                    button.innerHTML = originalHTML;
                }, 2000);

                e.clearSelection();
            });

            // Kiểm tra trường số người xem ảo
            const virtualViewersInput = document.getElementById('virtual_viewers');

            virtualViewersInput.addEventListener('input', function (e) {
                const value = e.target.value;
                const errorDiv = document.getElementById('virtual-viewers-error');

                // Kiểm tra nếu giá trị là một số hoặc một khoảng hợp lệ
                if (isValidViewerInput(value)) {
                    virtualViewersInput.classList.remove('is-invalid');
                    errorDiv.style.display = 'none';
                } else {
                    virtualViewersInput.classList.add('is-invalid');
                    errorDiv.style.display = 'block';
                }
            });

            // Hàm kiểm tra giá trị hợp lệ cho số người xem
            function isValidViewerInput(value) {
                return true;
                // Kiểm tra nếu là một số từ 10-1000
                if (!isNaN(value)) {
                    return true;
                }


                return false;
            }

            // Schedule management
            let scheduleIndex = {{ count($webinar->schedules ?? [0]) - 1 }};

            // Add new schedule
            document.getElementById('add-schedule').addEventListener('click', function () {
                scheduleIndex++;
                const container = document.getElementById('schedules-container');

                const scheduleItem = document.createElement('div');
                scheduleItem.className = 'schedule-item mb-3 card';
                scheduleItem.innerHTML = `
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-5">
                            <label class="form-label">Ngày</label>
                            <input type="date" class="form-control" name="schedules[` + scheduleIndex + `][date]" min="{{ date('Y-m-d') }}">
                        </div>
                        <div class="col-md-5">
                            <label class="form-label">Giờ</label>
                            <input type="time" class="form-control" name="schedules[` + scheduleIndex + `][time]">
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <button type="button" class="btn btn-danger remove-schedule mt-2 w-100">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `;

                container.appendChild(scheduleItem);

                // Enable first schedule delete button if we have more than one schedule
                if (container.querySelectorAll('.schedule-item').length > 1) {
                    document.querySelector('.remove-schedule[disabled]')?.removeAttribute('disabled');
                }

                // Thêm event listener cho input mới
                const newInputs = scheduleItem.querySelectorAll('input');
                newInputs.forEach(input => {
                    input.addEventListener('input', function () {
                        this.classList.remove('is-invalid');
                    });
                });
            });

            // Remove schedule
            document.getElementById('schedules-container').addEventListener('click', function (e) {
                if (e.target.closest('.remove-schedule')) {
                    const button = e.target.closest('.remove-schedule');
                    const scheduleItem = button.closest('.schedule-item');

                    const container = document.getElementById('schedules-container');
                    const items = container.querySelectorAll('.schedule-item');

                    if (items.length > 1) {
                        scheduleItem.remove();

                        // If only one schedule left, disable its delete button
                        if (container.querySelectorAll('.schedule-item').length === 1) {
                            container.querySelector('.remove-schedule').setAttribute('disabled', 'disabled');
                        }
                    }
                }
            });

            // Xử lý nút tạo mới đường dẫn tham gia
            document.getElementById('regenerate-url').addEventListener('click', function () {
                // Hiển thị xác nhận trước khi tạo mới
                Swal.fire({
                    title: 'Xác nhận tạo mới?',
                    text: 'Đường dẫn tham gia cũ sẽ không còn hoạt động sau khi tạo mới. Bạn có chắc chắn muốn tiếp tục?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Tạo mới',
                    cancelButtonText: 'Hủy'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Hiển thị loading
                        Swal.fire({
                            title: 'Đang xử lý...',
                            html: 'Vui lòng đợi trong giây lát',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        // Gửi request AJAX để tạo mới đường dẫn
                        fetch('{{ route('webinars.regenerate-url', $webinar) }}', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'X-CSRF-TOKEN': '{{ csrf_token() }}'
                            },
                            credentials: 'same-origin'
                        })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    // Cập nhật giá trị input
                                    document.getElementById('join_url').value = data.join_url;

                                    // Hiển thị thông báo thành công
                                    Swal.fire({
                                        title: 'Thành công',
                                        text: data.message,
                                        icon: 'success',
                                        confirmButtonText: 'Đã hiểu'
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Lỗi',
                                        text: data.message,
                                        icon: 'error',
                                        confirmButtonText: 'Đã hiểu'
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                                Swal.fire({
                                    title: 'Lỗi',
                                    text: 'Đã xảy ra lỗi khi tạo đường dẫn mới',
                                    icon: 'error',
                                    confirmButtonText: 'Đã hiểu'
                                });
                            });
                    }
                });
            });

            // Comments management
            let commentIndex = {{ $webinar->seeded_comments ? count($webinar->seeded_comments) : 1 }};

            document.getElementById('add-comment-btn').addEventListener('click', function () {
                const commentsContainer = document.getElementById('comments-container');

                const commentDiv = document.createElement('div');
                commentDiv.className = 'comment-item mb-2 d-flex';
                commentDiv.innerHTML = `
                <input type="text" class="form-control form-control-sm me-2"
                       name="seeded_comments[${commentIndex}][time]"
                       placeholder="00:00:00"
                       value="00:00:10">

                <input type="text" class="form-control form-control-sm me-2"
                       name="seeded_comments[${commentIndex}][name]"
                       placeholder="Tên người bình luận"
                       value="Đạt Tôi">

                <input type="text" class="form-control form-control-sm me-2"
                       name="seeded_comments[${commentIndex}][content]"
                       placeholder="Nội dung bình luận"
                       value="cần mua áo">

                <button type="button" class="btn btn-sm btn-danger delete-comment-btn">
                    <i class="fas fa-times"></i>
                </button>
            `;

                commentsContainer.appendChild(commentDiv);
                commentIndex++;
            });

            // Delete comment button handler
            document.getElementById('comments-container').addEventListener('click', function (e) {
                if (e.target.closest('.delete-comment-btn')) {
                    const button = e.target.closest('.delete-comment-btn');
                    const commentItem = button.closest('.comment-item');
                    commentItem.remove();
                }
            });

            // Excel upload button
            document.getElementById('upload-excel-btn').addEventListener('click', function () {
                Swal.fire({
                    title: 'Upload Excel File',
                    html: `
                    <div class="mb-3">
                        <label for="excel-file" class="form-label">Chọn file Excel</label>
                        <input class="form-control" type="file" id="excel-file" accept=".xlsx, .xls">
                    </div>
                    <div class="alert alert-info">
                        File Excel phải có 3 cột: Thời gian, Tên, Nội dung
                    </div>
                `,
                    showCancelButton: true,
                    cancelButtonText: 'Hủy',
                    confirmButtonText: 'Upload',
                    confirmButtonColor: '#28a745',
                    showLoaderOnConfirm: true,
                    preConfirm: () => {
                        const fileInput = document.getElementById('excel-file');
                        if (!fileInput.files[0]) {
                            Swal.showValidationMessage('Vui lòng chọn file Excel');
                            return false;
                        }

                        // Placeholder - would implement actual Excel parsing in a real app
                        // For now, just simulate success
                        return new Promise(resolve => {
                            setTimeout(() => {
                                resolve(true);
                            }, 1500);
                        });
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: 'Upload thành công!',
                            text: 'Dữ liệu bình luận đã được nhập từ Excel',
                            icon: 'success'
                        });
                    }
                });
            });

            // Generate random comments button
            document.getElementById('generate-comments-btn').addEventListener('click', function () {
                Swal.fire({
                    title: 'Tạo bình luận tự động',
                    html: `
                    <div class="mb-3">
                        <label class="form-label">Số lượng bình luận cần tạo</label>
                        <input type="number" id="comment-count" class="form-control" min="1" max="50" value="5">
                    </div>
                `,
                    showCancelButton: true,
                    cancelButtonText: 'Hủy',
                    confirmButtonText: 'Tạo bình luận',
                    confirmButtonColor: '#28a745',
                    preConfirm: () => {
                        const commentCount = document.getElementById('comment-count').value;
                        if (commentCount < 1 || commentCount > 50) {
                            Swal.showValidationMessage('Số lượng phải từ 1-50');
                            return false;
                        }
                        return parseInt(commentCount);
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        const commentCount = result.value;
                        const commentsContainer = document.getElementById('comments-container');

                        const sampleNames = ['Nguyễn Văn A', 'Trần Thị B', 'Lê Văn C', 'Phạm Thị D', 'Hoàng Văn E', 'Đạt Tôi', 'Mai Anh', 'Tuấn Vũ'];
                        const sampleContents = ['Sản phẩm này giá bao nhiêu ạ?', 'Cần tư vấn thêm', 'Có ship COD không ạ?', 'cần mua áo', 'Chất lượng sản phẩm thế nào?', 'Có giảm giá không ạ?'];

                        // Clear existing comments if user confirms
                        Swal.fire({
                            title: 'Xóa bình luận hiện tại?',
                            text: 'Bạn có muốn xóa tất cả bình luận hiện tại không?',
                            icon: 'question',
                            showCancelButton: true,
                            confirmButtonText: 'Có, xóa tất cả',
                            cancelButtonText: 'Không, giữ lại'
                        }).then((deleteResult) => {
                            if (deleteResult.isConfirmed) {
                                commentsContainer.innerHTML = '';
                            }

                            // Generate new comments
                            for (let i = 0; i < commentCount; i++) {
                                // Generate random time (0-60 minutes)
                                const minutes = Math.floor(Math.random() * 60);
                                const seconds = Math.floor(Math.random() * 60);
                                const timeStr = `00:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                                // Pick random name and content
                                const name = sampleNames[Math.floor(Math.random() * sampleNames.length)];
                                const content = sampleContents[Math.floor(Math.random() * sampleContents.length)];

                                const commentDiv = document.createElement('div');
                                commentDiv.className = 'comment-item mb-2 d-flex';
                                commentDiv.innerHTML = `
                                <input type="text" class="form-control form-control-sm me-2"
                                       name="seeded_comments[${commentIndex}][time]"
                                       placeholder="00:00:00"
                                       value="${timeStr}">

                                <input type="text" class="form-control form-control-sm me-2"
                                       name="seeded_comments[${commentIndex}][name]"
                                       placeholder="Tên người bình luận"
                                       value="${name}">

                                <input type="text" class="form-control form-control-sm me-2"
                                       name="seeded_comments[${commentIndex}][content]"
                                       placeholder="Nội dung bình luận"
                                       value="${content}">

                                <button type="button" class="btn btn-sm btn-danger delete-comment-btn">
                                    <i class="fas fa-times"></i>
                                </button>
                            `;

                                commentsContainer.appendChild(commentDiv);
                                commentIndex++;
                            }

                            Swal.fire({
                                title: 'Tạo thành công!',
                                text: `Đã tạo ${commentCount} bình luận ngẫu nhiên`,
                                icon: 'success'
                            });
                        });
                    }
                });
            });

            // Advertisement slots management
            let adSlotIndex = {{ $webinar->advertisement_slots ? count($webinar->advertisement_slots) : 1 }};

            // Add new advertisement slot
            document.getElementById('add-ad-slot-btn').addEventListener('click', function () {
                const slotsContainer = document.getElementById('advertisement-slots-container');

                const slotDiv = document.createElement('div');
                slotDiv.className = 'advertisement-slot mb-3 card';
                slotDiv.innerHTML = `
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">Thời gian hiển thị</label>
                            <input type="text" class="form-control"
                                   name="advertisement_slots[\${adSlotIndex}][time]"
                                   placeholder="00:00:00"
                                   value="00:00:10"
                                   pattern="[0-9]{2}:[0-9]{2}:[0-9]{2}">

                        </div>
                        <div class="col-md-7">
                            <label class="form-label">Chọn quảng cáo</label>
                            <select class="form-select" name="advertisement_slots[\${adSlotIndex}][advertisement_id]">
                                <option value="">-- Chọn quảng cáo --</option>
                                @foreach($advertisements as $ad)
                <option value="{{ $ad->id }}">
                                        {{ $ad->name ?? ($ad->type == 'image' ? 'Hình ảnh' : 'Sản phẩm') }}
                </option>
@endforeach
                </select>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-danger remove-ad-slot w-100">
                    <i class="fas fa-trash"></i> Xóa
                </button>
            </div>
        </div>
    </div>
`;

                slotsContainer.appendChild(slotDiv);
                adSlotIndex++;
            });

            // Remove advertisement slot button handler
            document.getElementById('advertisement-slots-container').addEventListener('click', function (e) {
                if (e.target.closest('.remove-ad-slot')) {
                    const button = e.target.closest('.remove-ad-slot');
                    const slotItem = button.closest('.advertisement-slot');
                    slotItem.remove();
                }
            });
        });
    </script>
@endpush
