<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ $webinar->title }} - Phòng Webinar</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Video.js -->
    <link href="https://vjs.zencdn.net/8.3.0/video-js.css" rel="stylesheet" />
    
    <!-- SweetAlert2 CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    
    <style>
        body {
            background-color: #f0f2f5;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .webinar-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background-color: #4a6cf7;
            color: white;
            padding: 1rem 2rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }
        .webinar-title {
            font-size: 1.5rem;
            margin-bottom: 0;
        }
        .main-content {
            display: flex;
            flex-direction: column;
        }
        .video-container {
            background-color: #000;
            border-radius: 0;
            overflow: hidden;
            position: relative;
        }
        .info-section {
            background-color: white;
            padding: 1.5rem;
            border-radius: 0 0 0.5rem 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .chat-section {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            height: 400px;
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            padding: 1rem;
        }
        .chat-input {
            border-top: 1px solid #e9ecef;
            padding: 1rem;
        }
        .viewers-info {
            display: flex;
            align-items: center;
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 0.5rem;
        }
        .status-live {
            display: inline-block;
            background-color: #dc3545;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 600;
            margin-right: 1rem;
        }
        .message {
            margin-bottom: 1rem;
        }
        .message-author {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }
        .message-text {
            background-color: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            display: inline-block;
        }
        .message-time {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        .waiting-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            z-index: 10;
        }
        .waiting-text {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }
        .waiting-time {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 2rem;
        }
        .video-js {
            width: 100%;
            height: 0;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
        }
        .vjs-big-play-button {
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }
        .system-message {
            background-color: #f8f9fa;
            border-left: 3px solid #4a6cf7;
            padding: 0.5rem 0.75rem;
            margin-bottom: 1rem;
            font-style: italic;
            color: #6c757d;
        }
        @media (min-width: 992px) {
            .main-content {
                flex-direction: row;
                gap: 1rem;
            }
            .video-and-info {
                flex: 3;
            }
            .chat-section {
                flex: 1;
                margin-top: 0;
                height: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4 webinar-container">
        <div class="header">
            <h1 class="webinar-title">{{ $webinar->title }}</h1>
            <div class="viewers-info">
                <span class="status-live" id="liveStatus">LIVE</span>
                <span><i class="fas fa-users me-2"></i> <span id="viewerCount">{{ $virtualViewers }}</span> người xem</span>
            </div>
        </div>
        
        <div class="main-content">
            <div class="video-and-info">
                <div class="video-container">
                    @if($webinar->video_path)
                        <video 
                            id="webinar-video" 
                            class="video-js vjs-big-play-centered" 
                            controls 
                            preload="auto" 
                            poster="{{ asset('images/video-poster.jpg') }}"
                            data-setup='{}'>
                            <source src="{{ asset(str_replace('public/', 'storage/', $webinar->video_path)) }}" type="video/mp4">
                            <p class="vjs-no-js">
                                Để xem video này, vui lòng bật JavaScript và nâng cấp lên
                                trình duyệt hỗ trợ <a href="https://videojs.com/html5-video-support/" target="_blank">HTML5 video</a>
                            </p>
                        </video>
                    @else
                        @if(isset($schedule))
                            @php
                                $scheduledTime = \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time']);
                                $now = \Carbon\Carbon::now();
                                $diff = $scheduledTime->diffInSeconds($now, false);
                                $inWaiting = $diff < 0;
                            @endphp
                            
                            @if($inWaiting)
                                <div class="waiting-overlay">
                                    <div class="waiting-text">Webinar sẽ bắt đầu sau</div>
                                    <div class="waiting-time" id="countdown">--:--:--</div>
                                    <p class="text-muted">Vui lòng đợi trong khi chúng tôi chuẩn bị webinar</p>
                                    <div class="spinner-border text-light mt-3" role="status">
                                        <span class="visually-hidden">Đang tải...</span>
                                    </div>
                                </div>
                            @endif
                            
                            <div style="aspect-ratio: 16/9; background-color: #000; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center text-white p-4">
                                    <i class="fas fa-video mb-4" style="font-size: 3rem;"></i>
                                    <h3>Stream Webinar</h3>
                                    <p class="text-muted">Video sẽ xuất hiện khi webinar bắt đầu</p>
                                </div>
                            </div>
                        @else
                            <div style="aspect-ratio: 16/9; background-color: #000; display: flex; align-items: center; justify-content: center;">
                                <div class="text-center text-white p-4">
                                    <i class="fas fa-exclamation-circle mb-4" style="font-size: 3rem;"></i>
                                    <h3>Không có video</h3>
                                    <p class="text-muted">Webinar này không có nội dung video</p>
                                </div>
                            </div>
                        @endif
                    @endif
                </div>
                
                <div class="info-section">
                    <h4>Thông tin về webinar</h4>
                    <p>
                        <strong>Người thuyết trình:</strong> {{ $webinar->speaker }}
                    </p>
                    
                    @if(isset($schedule))
                        <p>
                            <strong>Lịch trình:</strong> 
                            {{ \Carbon\Carbon::parse($schedule['date'] . ' ' . $schedule['time'])->format('d/m/Y H:i') }}
                        </p>
                    @endif
                    
                    <div class="accordion" id="faqAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faqOne" aria-expanded="false" aria-controls="faqOne">
                                    Gặp vấn đề kỹ thuật?
                                </button>
                            </h2>
                            <div id="faqOne" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                                <div class="accordion-body">
                                    Nếu bạn gặp vấn đề kỹ thuật với video, hãy thử làm mới trình duyệt, kiểm tra kết nối internet, hoặc sử dụng trình duyệt khác.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="chat-section">
                <div class="p-3 border-bottom">
                    <h5 class="mb-0">Trò chuyện trực tiếp</h5>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="system-message">
                        Chào mừng bạn đến với webinar! Hãy đặt câu hỏi tại đây.
                    </div>
                </div>
                
                <form id="chat-form" class="p-3 border-top">
                    <div class="input-group">
                        <input type="text" id="chat-input" class="form-control" placeholder="Nhập bình luận của bạn..." required>
                        <input type="hidden" id="video-timestamp" value="">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Load Video.js player for recorded webinars
            if (document.getElementById('webinar-video')) {
                const player = videojs('webinar-video', {
                    controls: true,
                    responsive: true,
                    fluid: true,
                    aspectRatio: '16:9',
                    autoplay: false,
                    playbackRates: [0.5, 1, 1.5, 2]
                });
            }
            
            // Update viewer count
            const viewerCount = document.getElementById('viewerCount');
            let viewerValue = viewerCount.textContent;
            let minCount, maxCount, currentCount;
            
            // Kiểm tra nếu giá trị là một khoảng (range)
            if (viewerValue.includes('-')) {
                const range = viewerValue.split('-');
                minCount = parseInt(range[0]);
                maxCount = parseInt(range[1]);
                // Tạo một số ngẫu nhiên trong khoảng min-max
                currentCount = Math.floor(Math.random() * (maxCount - minCount + 1)) + minCount;
                viewerCount.textContent = currentCount;
            } else {
                // Nếu giá trị là một số
                currentCount = parseInt(viewerValue);
            }
            
            setInterval(function() {
                // Tạo sự thay đổi nhỏ (+1, 0, hoặc -1) trong số người xem
                let change = Math.floor(Math.random() * 5) - 2; // -2, -1, 0, 1, or 2
                
                if (viewerValue.includes('-')) {
                    // Nếu giá trị ban đầu là một khoảng, giữ trong khoảng đó
                    currentCount = Math.max(minCount, Math.min(maxCount, currentCount + change));
                } else {
                    // Nếu giá trị ban đầu là một số, biến động nhỏ quanh giá trị đó (tối thiểu 1)
                    currentCount = Math.max(1, currentCount + change);
                }
                
                viewerCount.textContent = currentCount;
            }, 8000);
            
            // Countdown timer for waiting
            const countdownElement = document.getElementById('countdown');
            if (countdownElement) {
                @if(isset($schedule) && isset($scheduledTime))
                    const scheduledTime = new Date("{{ $scheduledTime->format('Y-m-d H:i:s') }}").getTime();
                    
                    function updateCountdown() {
                        const now = new Date().getTime();
                        const diff = scheduledTime - now;
                        
                        if (diff <= 0) {
                            countdownElement.textContent = "00:00:00";
                            // Reload the page when countdown finishes
                            location.reload();
                            return;
                        }
                        
                        const hours = Math.floor(diff / (1000 * 60 * 60));
                        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
                        const seconds = Math.floor((diff % (1000 * 60)) / 1000);
                        
                        countdownElement.textContent = 
                            (hours < 10 ? "0" + hours : hours) + ":" + 
                            (minutes < 10 ? "0" + minutes : minutes) + ":" + 
                            (seconds < 10 ? "0" + seconds : seconds);
                    }
                    
                    updateCountdown();
                    setInterval(updateCountdown, 1000);
                @endif
            }
            
            // Real-time chat functionality
            const chatMessages = document.getElementById('chatMessages');
            const chatInput = document.getElementById('chat-input');
            const chatForm = document.getElementById('chat-form');
            const videoTimestampInput = document.getElementById('video-timestamp');
            const webinarCode = '{{ $webinar->join_code }}';
            const participantName = '{{ $participant ? $participant->name : "Khách" }}';
            
            // Load existing messages
            loadMessages();
            
            // Add event listener to form submit
            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                sendMessage();
            });
            
            // Send message function
            function sendMessage() {
                const messageText = chatInput.value.trim();
                if (!messageText) return;
                
                const videoTimestamp = videoTimestampInput.value;

                $.ajax({
                    url: '{{ route('join.comment', $webinar->join_code) }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        content: messageText,
                        video_timestamp: videoTimestamp
                    },
                    success: function(data) {
                        addMessageToChat(data);
                        chatInput.value = '';
                    },
                    error: function(xhr) {
                        console.error('Error sending message:', xhr);
                        Swal.fire({
                            icon: 'error',
                            title: 'Lỗi',
                            text: 'Không thể gửi bình luận. Vui lòng thử lại sau.',
                            timer: 3000,
                            timerProgressBar: true
                        });
                    }
                });
            }
            
            // Add a message to the chat
            function addMessageToChat(message) {
                const messageElement = document.createElement('div');
                messageElement.className = 'message';
                
                let timestampHtml = '';
                if (message.video_timestamp) {
                    timestampHtml = `<span class="timestamp badge bg-info ms-2">${message.video_timestamp}</span>`;
                }
                
                messageElement.innerHTML = `
                    <div class="message-header">
                        <span class="username">${message.participant.name}</span>
                        ${timestampHtml}
                        <span class="time">${message.created_at}</span>
                    </div>
                    <div class="message-content">${message.content}</div>
                `;
                
                chatMessages.appendChild(messageElement);
                chatMessages.scrollTop = chatMessages.scrollHeight;
            }
            
            // Load existing messages
            function loadMessages() {
                $.ajax({
                    url: '{{ route('join.comments', $webinar->join_code) }}',
                    type: 'GET',
                    success: function(data) {
                        // Clear existing messages
                        chatMessages.innerHTML = '';
                        
                        // Add messages in reverse order (oldest first)
                        data.reverse().forEach(function(message) {
                            addMessageToChat(message);
                        });
                        
                        // Scroll to bottom
                        chatMessages.scrollTop = chatMessages.scrollHeight;
                    },
                    error: function(xhr) {
                        console.error('Error loading messages:', xhr);
                    }
                });
            }
            
            // Update video timestamp every second
            const videoElement = document.querySelector('video');
            
            if (videoElement) {
                setInterval(function() {
                    if (!videoElement.paused) {
                        const currentTime = videoElement.currentTime;
                        const hours = Math.floor(currentTime / 3600);
                        const minutes = Math.floor((currentTime % 3600) / 60);
                        const seconds = Math.floor(currentTime % 60);
                        
                        const timestamp = 
                            (hours < 10 ? '0' + hours : hours) + ':' +
                            (minutes < 10 ? '0' + minutes : minutes) + ':' +
                            (seconds < 10 ? '0' + seconds : seconds);
                        
                        videoTimestampInput.value = timestamp;
                    }
                }, 1000);
            }
            
            // Update live status
            const liveStatus = document.getElementById('liveStatus');
            if (liveStatus) {
                @if(isset($schedule) && !$inWaiting)
                    liveStatus.textContent = 'TRỰC TIẾP';
                    liveStatus.style.backgroundColor = '#dc3545';
                @elseif(isset($schedule) && $inWaiting)
                    liveStatus.textContent = 'CHỜ';
                    liveStatus.style.backgroundColor = '#ffc107';
                    liveStatus.style.color = '#000';
                @else
                    liveStatus.textContent = 'GHI LẠI';
                    liveStatus.style.backgroundColor = '#6c757d';
                @endif
            }
        });
    </script>
    
    <!-- Video.js -->
    <script src="https://vjs.zencdn.net/8.3.0/video.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    
    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    @include('sweetalert::alert')
</body>
</html> 