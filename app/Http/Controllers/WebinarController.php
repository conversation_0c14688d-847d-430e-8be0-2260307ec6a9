<?php

namespace App\Http\Controllers;

use App\Http\Requests\LiveStreamWebinarRequest;
use App\Jobs\saveCommentLivestreamWebninarJob;
use App\Models\Advertisement;
use App\Models\Question;
use App\Models\Webinar;
use App\Models\WebinarParticipant;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Log;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\User;

class WebinarController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware(function ($request, $next) {
            if (!auth()->user()->hasRole(['administrator', 'admin'])) {
                abort(403, '<PERSON>ạn không có quyền truy cập chức năng này.');
            }
            return $next($request);
        });
    }

    /**
     * Display a listing of the webinars.
     */
    public function index()
    {
        if (auth()->user()->can('webinars index')) {
            $webinars = Webinar::with('user')->orderBy('id', 'desc')->paginate(10);
        } elseif (auth()->user()->can('webinars view only you')) {
            $webinars = Auth::user()->webinars()->orderBy('id', 'desc')->paginate(10);
        } else {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        return view('webinars.index', compact('webinars'));
    }

    /**
     * Show the form for creating a new webinar.
     */
    public function create()
    {
        if (!auth()->user()->can('webinars create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        // Check if user has reached their webinar limit
        $user = Auth::user();
        if (!$user->hasRole('administrator') && $user->webinars()->count() >= $user->webinar_limit) {
            Alert::error('Giới hạn', 'Bạn đã đạt đến giới hạn webinar. Vui lòng liên hệ quản trị viên.');
            return redirect()->route('webinars.index');
        }

        // Get telegram settings for displaying bot info
        $telegramSettings = [];
        if (class_exists('\\App\\Models\\SiteSetting')) {
            $telegramBotUsername = \App\Models\SiteSetting::where('key', 'telegram_bot_name')->first();
            $telegramSettings['telegram_bot_username'] = $telegramBotUsername ? $telegramBotUsername->value : '';
        } else if (class_exists('\\App\\Models\\Setting')) {
            $telegramSettings['telegram_bot_username'] = \App\Models\Setting::get('telegram_bot_name', '');
        }

        // Get user's advertisements for displaying in the advertisements tab
        $advertisements = $user->advertisements;

        return view('webinars.create', compact('telegramSettings', 'advertisements'));
    }

    /**
     * Store a newly created webinar in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->can('webinars create')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $user = Auth::user();

        // Check if user has reached their webinar limit
        if (!$user->hasRole('administrator') && $user->webinars()->count() >= $user->webinar_limit) {
            Alert::error('Giới hạn', 'Bạn đã đạt đến giới hạn webinar. Vui lòng liên hệ quản trị viên.');
            return redirect()->route('webinars.index');
        }

        try {
            // Thực hiện validation từng phần để bắt lỗi chính xác hơn
            $basicRules = [
                'title' => 'required|string|max:255',
                'speaker' => 'required|string|max:255',
                'virtual_viewers' => [
                    'required',
                    function ($attribute, $value, $fail) {
                        // Kiểm tra nếu là số nguyên từ 10-1000

                    }
                ],
                'waiting_time' => 'required|integer|min:5|max:60',
                'schedules' => 'required|array|min:1',
            ];

            // Validate thông tin cơ bản trước
            $basicValidated = $request->validate($basicRules);

            // Kiểm tra và xử lý các lỗi của schedules nếu có
            $scheduleErrors = [];

            //            if ($request->has('schedules') && is_array($request->schedules)) {
//                foreach ($request->schedules as $index => $schedule) {
//                    // Kiểm tra date
//                    if (empty($schedule['date'])) {
//                        $scheduleErrors[] = "Ngày tại lịch trình #" . ($index + 1) . " không được để trống.";
//                    } elseif (!\Carbon\Carbon::parse($schedule['date'])->isAfter(\Carbon\Carbon::yesterday())) {
//                        $scheduleErrors[] = "Ngày tại lịch trình #" . ($index + 1) . " phải sau hoặc bằng ngày hôm nay.";
//                    }
//
//                    // Kiểm tra time
//                    if (empty($schedule['time'])) {
//                        $scheduleErrors[] = "Giờ tại lịch trình #" . ($index + 1) . " không được để trống.";
//                    } elseif (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $schedule['time'])) {
//                        $scheduleErrors[] = "Giờ tại lịch trình #" . ($index + 1) . " không đúng định dạng (HH:MM).";
//                    }
//                }
//            }
//
//            // Nếu có lỗi trong lịch trình, hiển thị thông báo
//            if (!empty($scheduleErrors)) {
//                $errorMessage = '<ul class="mb-0">';
//                foreach ($scheduleErrors as $error) {
//                    $errorMessage .= '<li>' . $error . '</li>';
//                }
//                $errorMessage .= '</ul>';
//
//                Alert::html('Lỗi lịch trình', $errorMessage, 'error');
//                return redirect()->back()->withInput()
//                    ->with('tab', 'schedule');
//            }

            // Generate a unique join code
            $joinCode = Webinar::generateJoinCode();
            $joinUrl = url("/join/{$joinCode}");

            // Prepare join settings
            $joinSettings = [
                'name_required' => $request->has('name_required'),
                'phone_required' => $request->has('phone_required'),
                'email_required' => $request->has('email_required'),
            ];

            // Prepare notification settings
            $notificationSettings = [
                'telegram_notify_participants' => $request->has('telegram_notify_participants') ? '1' : '0',
                'telegram_notify_comments' => $request->has('telegram_notify_comments') ? '1' : '0',
                'telegram_notify_order_new' => $request->has('telegram_notify_order_new') ? '1' : '0',
                'telegram_notify_order_payment_success' => $request->has('telegram_notify_order_payment_success') ? '1' : '0',
            ];

            // Create the webinar
            $webinar = new Webinar([
                'user_id' => $user->id,
                'title' => $basicValidated['title'],
                'speaker' => $basicValidated['speaker'],
                'virtual_viewers' => $basicValidated['virtual_viewers'],
                'waiting_time' => $basicValidated['waiting_time'],
                'join_settings' => $joinSettings,
                'join_code' => $joinCode,
                'join_url' => $joinUrl,
                'pin_comment' => $request->input('pin_comment'),
                'schedules' => $request->input('schedules'),
            ]);

            // Update notification settings
            $webinar->notification_settings = $notificationSettings;

            // Lưu bình luận seeding nếu có
            if ($request->has('seeded_comments')) {
                $webinar->seeded_comments = array_values(array_filter($request->seeded_comments, function ($comment) {
                    // Lọc bỏ các bình luận trống
                    return !empty($comment['content']);
                }));
            }

            // Lưu cấu hình quảng cáo nếu có
            if ($request->has('advertisement_slots')) {
                $webinar->advertisement_slots = array_values(array_filter($request->advertisement_slots, function ($slot) {
                    // Lọc bỏ các slot không có thời gian hoặc không chọn quảng cáo
                    return !empty($slot['time']) && !empty($slot['advertisement_id']);
                }));
            }

            $webinar->save();

            // Kiểm tra xem có tab nào đang active hay không để duy trì tab
            if ($request->has('active_tab')) {
                Alert::success('Thành công', 'Tạo webinar thành công!');
                return redirect()->route('webinars.edit', $webinar)
                    ->with('tab', $request->input('active_tab'));
            } else {
                Alert::success('Thành công', 'Tạo webinar thành công!');
                return redirect()->route('webinars.show', $webinar);
            }

        } catch (ValidationException $e) {
            $errors = $e->validator->errors()->all();
            $errorMessage = '<ul class="mb-0">';
            foreach ($errors as $error) {
                $errorMessage .= '<li>' . $error . '</li>';
            }
            $errorMessage .= '</ul>';

            Alert::html('Lỗi dữ liệu', $errorMessage, 'error');
            return redirect()->back()->withInput();
        } catch (\Exception $e) {
            Alert::error('Lỗi hệ thống', 'Đã xảy ra lỗi khi tạo webinar: ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function livestreamManager(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $advertisements = Advertisement::pluck("name", "id")->toArray();
        $order = $webinar->orders()
            ->whereDate("created_at", Carbon::now())
            ->where("is_live", 1);
        $total_view = 0;
        if (Cache::has("live_viewers:$webinar->id")) {
            $total_view = count(Cache::get("live_viewers:$webinar->id", []));
        }
        $views = count(Cache::get("live_unique_views:$webinar->id", []));
        $total_view = number_format($total_view);
        $total_view_online = number_format($views);
        $total_order_paid = number_format($order->clone()->where("payment_status", "paid")->count());
        $total_order_pending = number_format($order->clone()->where("payment_status", "pending")->count());
        $total_amount_order = number_format($order->clone()->where("payment_status", "paid")->sum("price"));
        return view('webinars.livestream', compact('webinar', 'total_view_online', 'advertisements', 'total_view', 'total_order_paid', 'total_order_pending', 'total_amount_order'));
    }

    public function livestreamLearningManager(Webinar $webinar)
    {
        // Lấy ra danh sách bộ câu hỏi của webinar
        $questionSets = $webinar->questionSets;

        return view("webinars.livestream_learning", compact("webinar", "questionSets"));
    }

    /**
     * Get overall stats for webinar (cho dashboard)
     */
    private function getOverallStats($webinarId)
    {
        $totalQuestions = \App\Models\Question::where('webinar_id', $webinarId)->count();
        $activeQuestions = \App\Models\Question::where('webinar_id', $webinarId)->where('status', true)->count();
        $totalResponses = \App\Models\QuestionResponse::where('webinar_id', $webinarId)->count();
        $todayResponses = \App\Models\QuestionResponse::where('webinar_id', $webinarId)->whereDate('responded_at', today())->count();
        $realtimeResponses = \App\Models\QuestionResponse::where('webinar_id', $webinarId)->where('responded_at', '>=', now()->subMinutes(5))->count();

        // Unique participants (based on session_id)
        $uniqueParticipants = \App\Models\QuestionResponse::where('webinar_id', $webinarId)
            ->distinct('session_id')
            ->count();

        // Response rate
        $responseRate = $uniqueParticipants > 0 && $activeQuestions > 0
            ? round(($totalResponses / ($uniqueParticipants * $activeQuestions)) * 100, 1)
            : 0;

        // Stats by question type
        $statsByType = \App\Models\Question::where('webinar_id', $webinarId)
            ->select('type', \Illuminate\Support\Facades\DB::raw('count(*) as count'))
            ->groupBy('type')
            ->get()
            ->keyBy('type');

        return [
            'total_questions' => $totalQuestions,
            'active_questions' => $activeQuestions,
            'total_responses' => $totalResponses,
            'today_responses' => $todayResponses,
            'realtime_responses' => $realtimeResponses,
            'unique_participants' => $uniqueParticipants,
            'response_rate' => $responseRate,
            'stats_by_type' => $statsByType,
            'updated_at' => now()->toISOString()
        ];
    }

    public function UpdateLivestreamManager(Webinar $webinar, LiveStreamWebinarRequest $request)
    {
        if (!auth()->user()->can('webinars show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $data = $webinar->livestreams ?: [];
        $data["is_enabled"] = 0;//Trạng thái hiển thị quảng cáo cho livestream thường
        $data["is_enabled_question"] = 0;//Trạng thái hiển thị câu hỏi cho livestream lớp học
        $data["questions"] = [];//Xóa Câu hỏi hiển thị
        $data["advertisements"] = [];//Xóa quảng cáo đang hiển thị
        $data["question_id"] = null;
        $data["question_set_id"] = null;

        $data["type"] = $request->input("type_live");
        $data["link"] = $request->input("link_live");


        if ($request->has("is_live_learning")) {
            $data["is_live_learning"] = $request->input("is_live_learning");
            $data["is_live"] = 0;
        } else {
            $data["is_live_learning"] = 0;
            $data["is_live"] = $request->input("is_live");
        }

        if ($data["is_live"] == 1 || $data["is_live_learning"] == 1) {

            $mess = "Phiên live đã bắt đầu";
        } else {
            $views = count(Cache::get("live_unique_views:$webinar->id", []));
            dispatch(new saveCommentLivestreamWebninarJob($webinar));
            $mess = "Phiên live đã kết thúc";
        }
        Cache::forget("live_viewers:$webinar->id");
        Cache::forget("live_unique_views:$webinar->id");
        $webinar->update([
            "livestreams" => $data
        ]);

        Alert::success($mess);
        return redirect()->back();
    }


    public function toggleQuestion(Webinar $webinar, Request $request)
    {

        if (!auth()->user()->can('webinars show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $data = $webinar->livestreams ?: [];

        if ($data["is_live_learning"] == 0) {
            return response()->json([
                "error" => true,
                "mess" => "Hãy bật livestream trước!"
            ]);
        }
        $status = $request->input("status");
        $data["question_id"] = $request->input("question_id");
        $data["question_set_id"] = $request->input("question_set_id");
        Cache::forget("livestream_ads");
        if ($status) {
            $data["default_code"] = Str::random(10);
            $data["is_enabled_question"] = 1;
            $question = Question::find($data["question_id"]);
            $type = $question->type;
            if ($type == "multiple-choice") {

            }
            $data["questions"] = [
                "id" => $question->id,
                "type" => $question->type,
                "title" => "",
                "description" => $question->description,
                "question" => $question->title,
                "options" => $question->answers
            ];
            $mess = "Hiển thị câu hỏi!";
        } else {
            $data["default_code"] = null;
            $data["question_id"] = null;
            $data["question_set_id"] = null;
            $data["is_enabled_question"] = 0;
            $data["questions"] = [];
            $mess = "Ẩn câu hỏi!";
        }
        $webinar->update([
            "livestreams" => $data
        ]);

        return response()->json([
            "error" => false,
            "mess" => $mess,
            "flag" => $data["is_enabled_question"]
        ]);
    }

    public function pushAdvertisement(Webinar $webinar, Request $request)
    {
        if (!auth()->user()->can('webinars show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $data = $webinar->livestreams ?: [];
        if (empty($data) || $data["is_live"] == 0) {
            return response()->json([
                "error" => true,
                "mess" => "Phiên live chưa bắt đầu!",
            ]);
        }
        $status = $request->input("status");
        $advertisement_id = $request->input("advertisement_id");
        if (!$advertisement_id) {
            return response()->json([
                "error" => true,
                "mess" => "Bạn chưa chọn quảng cáo!",
            ]);
        }
        Cache::forget("livestream_ads");
        if ($status) {
            $data["is_enabled"] = 1;
            $data["default_code"] = Str::random(10);
            $advertisement = Advertisement::find($request->input("advertisement_id"));
            $data["advertisement_id"] = $advertisement_id;
            if (is_object($advertisement)) {
                $product = $advertisement->product;
                $price = (int)$advertisement->sale_price;
                $oldPrice = (int)$advertisement->original_price;

                $discount = $oldPrice > 0 ? ($oldPrice - $price) * 100 / $oldPrice : 0;
                $data["advertisements"] = [
                    "id" => $advertisement->id,
                    "name" => $advertisement->name,
                    "type" => $advertisement->type,
                    "order_method" => $advertisement->order_method,
                    "at" => null, // Show at 5 seconds into the video
                    "showIn" => $advertisement->display_time, // Stays open for 10 seconds
                    "image" => $advertisement->image ? Storage::disk("public")->url($advertisement->image) : null,
                    "image_url" => $advertisement->url,
                    "redirect_url_ads" => $advertisement->redirect_url,
                    "price" => number_format($price) . " ₫",
                    "oldPrice" => number_format($oldPrice) . " ₫",
                    "detail" => [
                        "id" => @$product->id,
                        "title" => @$product->name, // More specific title
                        "image" => is_object($product) && !empty($product->image) ? Storage::disk("public")->url($product->image) : null,
                        "tags" => @$product->tag,
                        "description" => null,
                        "price" => number_format($price) . " ₫",
                        "oldPrice" => number_format($oldPrice) . " ₫",
                        "discount" => ceil($discount) . "%",
                        "quantity" => @$product->stock_quantity,
                        "totalSold" => $advertisement->virtual_quantity,
                    ],
                    "form_required" => [
                        "name" => $advertisement->require_name,
                        "email" => $advertisement->require_email,
                        "phone" => $advertisement->require_phone,
                    ]
                ];
            }
            $mess = "Hiển thị quảng cáo!";
        } else {
            $data["is_enabled"] = 0;
            $data["advertisements"] = [];
            $mess = "Ẩn quảng cáo!";
        }

        $webinar->update([
            "livestreams" => $data
        ]);
        return response()->json([
            "error" => false,
            "mess" => $mess,
            "flag" => $data["is_enabled"]
        ]);
    }

    public function getDataLivestream(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars index')) {
            return response()->json([
                "error" => true,
                "data" => []
            ]);
        }
        $livestreams = $webinar->livestreams;
        $livestreams["time_last_online"] = Carbon::now("Asia/Ho_Chi_Minh")->toDateTimeString();

        $webinar->update(["livestreams" => $livestreams]);
        $data["total_view"] = $webinar->total_view;
        $order = $webinar->orders()
            ->whereDate("created_at", Carbon::now())
            ->where("is_live", 1);
        $total_order_paid = $order->clone()->where("payment_status", "paid")->count();
        $total_order_pending = $order->clone()->where("payment_status", "pending")->count();
        $total_amount_order = $order->clone()->where("payment_status", "paid")->sum("price");
        $total_view = count(Cache::get("live_viewers:$webinar->id", []));
        $views = count(Cache::get("live_unique_views:$webinar->id", []));
        $data["total_view_online"] = number_format($total_view);
        $data["total_view"] = number_format($views);
        $data["total_order_paid"] = number_format($total_order_paid);
        $data["total_order_pending"] = number_format($total_order_pending);
        $data["total_amount_order"] = number_format($total_amount_order);
        return response()->json([
            "error" => false,
            "data" => $data,
            "cache" => Cache::get("webinar_online_{$webinar->id}")
        ]);
    }

    /**
     * Display the specified webinar.
     * @param Webinar $webinar
     * @return \Illuminate\Container\Container|mixed|object
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function show(Webinar $webinar)
    {

        if (!auth()->user()->can('webinars show')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('view', $webinar);
        return view('webinars.show', compact('webinar'));
    }

    /**
     * Show the form for editing the specified webinar.
     */
    public function edit(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $webinar);

        // Get telegram settings for displaying bot info
        $telegramSettings = [];
        if (class_exists('\\App\\Models\\SiteSetting')) {
            $telegramBotUsername = \App\Models\SiteSetting::where('key', 'telegram_bot_name')->first();
            $telegramSettings['telegram_bot_username'] = $telegramBotUsername ? $telegramBotUsername->value : '';
        } else if (class_exists('\\App\\Models\\Setting')) {
            $telegramSettings['telegram_bot_username'] = \App\Models\Setting::get('telegram_bot_name', '');
        }

        // Get user's advertisements for displaying in the advertisements tab
        $advertisements = $webinar->user->advertisements;

        return view('webinars.edit', compact('webinar', 'telegramSettings', 'advertisements'));
    }

    /**
     * Update the specified webinar in storage.
     */
    public function update(Request $request, Webinar $webinar)
    {
        if (!auth()->user()->can('webinars update')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $webinar);

        try {
            // Thực hiện validation từng phần để bắt lỗi chính xác hơn
            $basicRules = [
                'title' => 'required|string|max:255',
                'speaker' => 'required|string|max:255',
                'virtual_viewers' => [
                    'required',
                    function ($attribute, $value, $fail) {


                    }
                ],
                'waiting_time' => 'required|integer|min:5|max:60',
                'schedules' => 'required|array|min:1',
            ];

            // Validate thông tin cơ bản trước
            $basicValidated = $request->validate($basicRules);

            // Kiểm tra và xử lý các lỗi của schedules nếu có
            $scheduleErrors = [];

            //            if ($request->has('schedules') && is_array($request->schedules)) {
//                foreach ($request->schedules as $index => $schedule) {
//                    // Kiểm tra date
//                    if (empty($schedule['date'])) {
//                        $scheduleErrors[] = "Ngày tại lịch trình #" . ($index + 1) . " không được để trống.";
//                    }
//
//                    // Kiểm tra time
//                    if (empty($schedule['time'])) {
//                        $scheduleErrors[] = "Giờ tại lịch trình #" . ($index + 1) . " không được để trống.";
//                    } elseif (!preg_match('/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $schedule['time'])) {
//                        $scheduleErrors[] = "Giờ tại lịch trình #" . ($index + 1) . " không đúng định dạng (HH:MM).";
//                    }
//                }
//            }
//
//            // Nếu có lỗi trong lịch trình, hiển thị thông báo
//            if (!empty($scheduleErrors)) {
//                $errorMessage = '<ul class="mb-0">';
//                foreach ($scheduleErrors as $error) {
//                    $errorMessage .= '<li>' . $error . '</li>';
//                }
//                $errorMessage .= '</ul>';
//
//                Alert::html('Lỗi lịch trình', $errorMessage, 'error');
//                return redirect()->back()->withInput()
//                    ->with('tab', 'schedule');
//            }

            // Update the webinar
            $webinar->title = $basicValidated['title'];
            $webinar->speaker = $basicValidated['speaker'];
            $webinar->virtual_viewers = $basicValidated['virtual_viewers'];
            $webinar->waiting_time = $basicValidated['waiting_time'];
            $webinar->schedules = $request->schedules;
            $webinar->allow_replay = $request->has('allow_replay');
            $webinar->only_show_my_comment = $request->has('only_show_my_comment');
            $webinar->pin_comment = $request->input('pin_comment');
            // Update join settings
            $webinar->join_settings = [
                'name_required' => $request->has('name_required'),
                'phone_required' => $request->has('phone_required'),
                'email_required' => $request->has('email_required'),
            ];

            // Update notification settings
            $webinar->notification_settings = [
                'telegram_notify_participants' => $request->has('telegram_notify_participants') ? '1' : '0',
                'telegram_notify_comments' => $request->has('telegram_notify_comments') ? '1' : '0',
                'telegram_notify_order_new' => $request->has('telegram_notify_order_new') ? '1' : '0',
                'telegram_notify_order_payment_success' => $request->has('telegram_notify_order_payment_success') ? '1' : '0',
            ];

            // Lưu bình luận seeding nếu có
            if ($request->has('seeded_comments')) {
                $webinar->seeded_comments = array_values(array_filter($request->seeded_comments, function ($comment) {
                    // Lọc bỏ các bình luận trống
                    return !empty($comment['content']);
                }));
            }

            // Lưu cấu hình quảng cáo nếu có
            if ($request->has('advertisement_slots')) {
                $webinar->advertisement_slots = array_values(array_filter($request->advertisement_slots, function ($slot) {
                    // Lọc bỏ các slot không có thời gian hoặc không chọn quảng cáo
                    return !empty($slot['time']) && !empty($slot['advertisement_id']);
                }));
            }

            $webinar->save();

            // Kiểm tra xem có phải đang ở tab bình luận hay không để duy trì tab
            if ($request->has('active_tab')) {
                return redirect()->route('webinars.edit', $webinar)
                    ->with('success', 'Cập nhật webinar thành công!')
                    ->with('tab', $request->input('active_tab'));
            }

            return redirect()->route('webinars.show', $webinar)
                ->with('success', 'Cập nhật webinar thành công!');

        } catch (ValidationException $e) {
            $errors = $e->validator->errors()->all();
            $errorMessage = '<ul class="mb-0">';
            foreach ($errors as $error) {
                $errorMessage .= '<li>' . $error . '</li>';
            }
            $errorMessage .= '</ul>';

            return redirect()->back()->withInput()
                ->with('error', $errorMessage);
        } catch (\Exception $e) {
            return redirect()->back()->withInput()
                ->with('error', 'Đã xảy ra lỗi khi cập nhật webinar: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified webinar from storage.
     */
    public function destroy(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('delete', $webinar);

        try {
            // Soft delete the webinar (files will be cleaned up later by command)
            $webinar->delete();

            Alert::success('Thành công', 'Webinar đã được xóa! Files sẽ được dọn dẹp tự động sau vài ngày.');
            return redirect()->route('webinars.index');
        } catch (\Exception $e) {
            Alert::error('Lỗi', 'Đã xảy ra lỗi khi xóa webinar: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Show trashed webinars.
     */
    public function trashed()
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        $webinars = Webinar::onlyTrashed()
            ->with('user')
            ->orderBy('deleted_at', 'desc')
            ->paginate(10);

        return view('webinars.trashed', compact('webinars'));
    }

    /**
     * Restore a trashed webinar.
     */
    public function restore($id)
    {
        if (!auth()->user()->can('webinars delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        try {
            $webinar = Webinar::onlyTrashed()->findOrFail($id);
            $this->authorize('delete', $webinar);

            $webinar->restore();

            Alert::success('Thành công', 'Khôi phục webinar thành công!');
            return redirect()->route('webinars.trashed');
        } catch (\Exception $e) {
            Alert::error('Lỗi', 'Đã xảy ra lỗi khi khôi phục webinar: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Force delete a trashed webinar.
     */
    public function forceDelete($id)
    {
        if (!auth()->user()->can('webinars delete')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }

        try {
            $webinar = Webinar::onlyTrashed()->findOrFail($id);
            $this->authorize('delete', $webinar);

            // Delete files immediately
            if ($webinar->video_path) {
                if (str_starts_with($webinar->video_path, 's3://') || $webinar->s3_url) {
                    // Delete S3 file
                    $this->deleteS3File($webinar);
                } else {
                    // Delete local file
                    Storage::delete($webinar->video_path);
                }
            }

            $webinar->forceDelete();

            Alert::success('Thành công', 'Xóa vĩnh viễn webinar thành công!');
            return redirect()->route('webinars.trashed');
        } catch (\Exception $e) {
            Alert::error('Lỗi', 'Đã xảy ra lỗi khi xóa vĩnh viễn webinar: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * Delete S3 file for webinar.
     */
    private function deleteS3File(Webinar $webinar)
    {
        try {
            if ($webinar->s3_url) {
                // Extract key from S3 URL
                $parsedUrl = parse_url($webinar->s3_url);
                if (isset($parsedUrl['path'])) {
                    $path = ltrim($parsedUrl['path'], '/');
                    $bucket = config('filesystems.disks.vns3.bucket');

                    if (str_starts_with($path, $bucket . '/')) {
                        $key = substr($path, strlen($bucket) + 1);
                    } else {
                        $key = $path;
                    }

                    if (Storage::disk('vns3')->exists($key)) {
                        Storage::disk('vns3')->delete($key);
                    }
                }
            }
        } catch (\Exception $e) {
            \Log::error('Error deleting S3 file', [
                'webinar_id' => $webinar->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Show the form for uploading a video.
     */
    public function showUploadForm(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $webinar);

        // Increase PHP limits for large file uploads
        ini_set('upload_max_filesize', '5G');
        ini_set('post_max_size', '5G');
        ini_set('memory_limit', '512M');
        ini_set('max_execution_time', '300');
        ini_set('max_input_time', '300');

        return view('webinars.upload', compact('webinar'));
    }

    /**
     * Handle video chunk upload.
     */
    public function uploadVideo(Request $request, Webinar $webinar)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $webinar);

        try {
            // Check if this is a chunked upload for S3
            if ($request->has('use_s3') && $request->has('chunk') && $request->has('name')) {
                return $this->chunkedUploadS3($request, $webinar);
            }

            // Check if this is a JSON request with S3 data
            if ($request->isJson() && $request->has('s3_file_path') && $request->has('s3_key')) {
                // This is an S3 upload completion notification
                return $this->handleS3Upload($request, $webinar);
            }

            // Continue with original file upload logic
            // Validate the request
            $rules = [
                'file' => 'required|file|mimes:mp4|max:5242880', // 5GB = 5242880KB
                'chunk' => 'required|integer|min:0',
                'chunks' => 'required|integer|min:1',
            ];

            // More permissive MIME type validation to avoid issues
            $validator = \Validator::make($request->all(), $rules);
            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi xác thực: ' . implode(', ', $validator->errors()->all())
                ], 422);
            }

            $file = $request->file('file');
            $chunk = (int)$request->input('chunk');
            $chunks = (int)$request->input('chunks');
            $useS3 = $request->has('use_s3') && $request->input('use_s3') === 'true';

            if (!$file || !$file->isValid()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tệp tin không hợp lệ hoặc không tìm thấy'
                ], 400);
            }

            // Log some debug information
            \Log::info('Uploading file', [
                'webinar_id' => $webinar->id,
                'chunk' => $chunk,
                'chunks' => $chunks,
                'file_size' => $file->getSize(),
                'file_name' => $file->getClientOriginalName(),
                'file_extension' => $file->getClientOriginalExtension(),
                'use_s3' => $useS3
            ]);

            // Generate a random filename with the original extension
            $originalExtension = $file->getClientOriginalExtension();
            if (empty($originalExtension)) {
                $originalExtension = 'mp4'; // Default to mp4 if no extension
            }
            $fileName = Str::random(40) . '.' . $originalExtension;

            // Check if it's a single-chunk upload and we should use S3
            if ($chunks == 1 && $chunk == 0 && $useS3) {
                return $this->uploadToS3($file, $webinar, $fileName);
            }

            // Continue with chunked upload to local storage
            // Setup paths
            $webinarDir = 'webinar/' . $webinar->id;
            $filePath = $webinarDir . '/' . $fileName;

            // Create the temp directory for chunks - make sure parent directories exist
            $tempBaseDir = storage_path('app/temp');
            if (!file_exists($tempBaseDir)) {
                if (!mkdir($tempBaseDir, 0777, true)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Không thể tạo thư mục tạm thời gốc: ' . $tempBaseDir
                    ], 500);
                }
            }

            $tempDir = $tempBaseDir . '/' . $webinar->id;
            if (!file_exists($tempDir)) {
                if (!mkdir($tempDir, 0777, true)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Không thể tạo thư mục tạm thời: ' . $tempDir
                    ], 500);
                }
            }

            // Save the chunk directly using the move method
            $chunkPath = $tempDir . '/chunk_' . $chunk;
            if (!$file->move(dirname($chunkPath), basename($chunkPath))) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không thể di chuyển tệp tạm thời đến: ' . $chunkPath
                ], 500);
            }

            // Check if this was the last chunk
            if ($chunk == $chunks - 1) {
                // Make sure storage/app/public directory exists
                $publicDir = storage_path('app/public');
                if (!file_exists($publicDir)) {
                    if (!mkdir($publicDir, 0777, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Không thể tạo thư mục public: ' . $publicDir
                        ], 500);
                    }
                }

                // Make sure the webinar directory exists within public storage
                $publicWebinarDir = $publicDir . '/' . $webinarDir;
                if (!file_exists(dirname($publicWebinarDir))) {
                    if (!mkdir(dirname($publicWebinarDir), 0777, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Không thể tạo thư mục webinar gốc: ' . dirname($publicWebinarDir)
                        ], 500);
                    }
                }

                if (!file_exists($publicWebinarDir)) {
                    if (!mkdir($publicWebinarDir, 0777, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Không thể tạo thư mục webinar: ' . $publicWebinarDir
                        ], 500);
                    }
                }

                // Set the final path
                $finalPath = $publicDir . '/' . $filePath;

                // Make sure the target directory exists
                $finalDir = dirname($finalPath);
                if (!file_exists($finalDir)) {
                    if (!mkdir($finalDir, 0777, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Không thể tạo thư mục đích: ' . $finalDir
                        ], 500);
                    }
                }

                // Open final file for writing
                $finalFile = @fopen($finalPath, 'wb');
                if (!$finalFile) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Không thể tạo tệp đích: ' . $finalPath . ' - ' . error_get_last()['message']
                    ], 500);
                }

                // Append each chunk to the final file
                $missingChunks = [];
                for ($i = 0; $i < $chunks; $i++) {
                    $chunkFile = $tempDir . '/chunk_' . $i;
                    if (file_exists($chunkFile)) {
                        $chunkContent = @file_get_contents($chunkFile);
                        if ($chunkContent === false) {
                            $missingChunks[] = $i;
                            continue;
                        }
                        fwrite($finalFile, $chunkContent);
                        @unlink($chunkFile); // Delete the chunk after it's appended
                    } else {
                        $missingChunks[] = $i;
                    }
                }

                fclose($finalFile);

                // If there were missing chunks, report an error
                if (!empty($missingChunks)) {
                    @unlink($finalPath); // Delete the incomplete file
                    return response()->json([
                        'success' => false,
                        'message' => 'Có lỗi khi tải lên: một số phần bị thiếu (' . implode(', ', $missingChunks) . ')'
                    ], 500);
                }

                // Clean up temp directory if it exists
                if (is_dir($tempDir)) {
                    $this->deleteDirectory($tempDir);
                }

                // Delete old video file if it exists
                if ($webinar->video_path) {
                    $oldVideoPath = storage_path('app/' . str_replace('public/', '', $webinar->video_path));
                    if (file_exists($oldVideoPath)) {
                        @unlink($oldVideoPath);
                    }
                }

                // Get the video duration (if available)
                $videoDuration = $request->input('video_duration');

                // Get file size
                $fileSize = $file->getSize();

                // Update webinar with the new video path
                $webinar->update([
                    'video_path' => 'public/' . $filePath,
                    'video_duration_minutes' => $videoDuration ? (int)$videoDuration : null,
                    'video_file_size' => $fileSize
                ]);

                // Create a symlink if it doesn't exist
                $this->ensurePublicSymlinkExists();

                return response()->json(['success' => true, 'message' => 'Tải lên video thành công']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã tải lên phần ' . ($chunk + 1) . '/' . $chunks,
                'chunk' => $chunk
            ]);

        } catch (\Exception $e) {
            \Log::error('File upload error: ' . $e->getMessage(), [
                'webinar_id' => $webinar->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage(),
                'file' => $request->hasFile('file') ? 'File exists' : 'No file'
            ], 500);
        }
    }

    /**
     * Handle upload completion notification from S3
     */
    private function handleS3Upload(Request $request, Webinar $webinar)
    {
        try {
            // Get the S3 file information
            $s3FilePath = $request->input('s3_file_path');
            $s3Key = $request->input('s3_key');

            Log::info('S3 upload complete', [
                'webinar_id' => $webinar->id,
                's3_file_path' => $s3FilePath,
                's3_key' => $s3Key
            ]);

            // Delete old video file if it exists
            if ($webinar->video_path) {
                // If it's an S3 path already
                if (strpos($webinar->video_path, 's3://') === 0) {
                    Log::info('Deleting old S3 file: ' . $webinar->video_path);
                    // Logic to delete from S3 if needed
                } else {
                    // It's a local file
                    $oldVideoPath = storage_path('app/' . str_replace('public/', '', $webinar->video_path));
                    if (file_exists($oldVideoPath)) {
                        @unlink($oldVideoPath);
                    }
                }
            }

            // Update webinar with the new S3 video path
            $s3Path = 's3://' . $s3Key;
            $webinar->update([
                'video_path' => $s3Path,
                's3_url' => $s3FilePath
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Video đã được tải lên thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('S3 upload handling error: ' . $e->getMessage(), [
                'webinar_id' => $webinar->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Lỗi xử lý dữ liệu từ S3: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload file directly to S3 from server side to avoid CORS issues
     */
    private function uploadToS3($file, $webinar, $fileName)
    {
        try {
            // Get the file path
            $filePath = $file->getRealPath();

            // Generate a unique key for the file
            $s3Key = 'webinar-' . $webinar->id . '/' . $fileName;

            // Clean up any existing S3 files for this webinar to avoid duplication
            $this->cleanupExistingS3Files($webinar);

            // Upload to S3 using Laravel's Storage facade - use the file directly instead of file_get_contents
            $fileStream = fopen($filePath, 'r');
            $uploaded = Storage::disk('vns3')->put($s3Key, $fileStream);
            fclose($fileStream);

            if (!$uploaded) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lỗi khi tải lên S3: không thể tải lên tệp'
                ], 500);
            }

            // Get the file URL - fixed to avoid empty path error
            $s3BaseUrl = rtrim(config('filesystems.disks.vns3.url'), '/');
            $bucket = config('filesystems.disks.vns3.bucket');
            $s3Url = $s3BaseUrl . '/' . $bucket . '/' . $s3Key;

            Log::info('S3 upload successful', [
                's3_key' => $s3Key,
                's3_url' => $s3Url
            ]);

            // Delete old video file if it exists
            if ($webinar->video_path) {
                // If it's a local file
                $oldVideoPath = storage_path('app/' . str_replace('public/', '', $webinar->video_path));
                if (file_exists($oldVideoPath)) {
                    @unlink($oldVideoPath);
                }
            }

            // Get the video duration from the request in the parent method
            $videoDuration = request()->input('video_duration');

            // Get file size
            $fileSize = $file->getSize();

            // Update webinar with the new S3 video path
            $s3Path = 's3://' . $s3Key;
            $webinar->update([
                'video_path' => $s3Path,
                's3_url' => $s3Url,
                'video_duration_minutes' => $videoDuration ? (int)$videoDuration : null,
                'video_file_size' => $fileSize
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Video đã được tải lên S3 thành công'
            ]);
        } catch (\Exception $e) {
            Log::error('S3 upload error: ' . $e->getMessage(), [
                'webinar_id' => $webinar->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi tải lên S3: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clean up existing S3 files for a webinar
     */
    private function cleanupExistingS3Files(Webinar $webinar)
    {
        try {
            // Get a list of all files in the webinar's S3 directory
            $prefix = 'webinar-' . $webinar->id . '/';
            $files = Storage::disk('vns3')->files($prefix);

            Log::info('Cleaning up existing S3 files', [
                'webinar_id' => $webinar->id,
                'file_count' => count($files)
            ]);

            foreach ($files as $file) {
                Storage::disk('vns3')->delete($file);
            }

        } catch (\Exception $e) {
            Log::warning('Error cleaning up S3 files: ' . $e->getMessage(), [
                'webinar_id' => $webinar->id
            ]);
        }
    }

    /**
     * Recursively delete a directory
     */
    private function deleteDirectory($dir)
    {
        if (!file_exists($dir)) {
            return true;
        }

        if (!is_dir($dir)) {
            return unlink($dir);
        }

        foreach (scandir($dir) as $item) {
            if ($item == '.' || $item == '..') {
                continue;
            }

            if (!$this->deleteDirectory($dir . DIRECTORY_SEPARATOR . $item)) {
                return false;
            }
        }

        return rmdir($dir);
    }

    /**
     * Ensure the public storage symlink exists.
     */
    private function ensurePublicSymlinkExists()
    {
        $linkPath = public_path('storage');
        $targetPath = storage_path('app/public');

        if (!file_exists($linkPath)) {
            if (PHP_OS_FAMILY === 'Windows') {
                // On Windows we need to use mklink command
                // First, make the public path use the correct slashes
                $windowsLinkPath = str_replace('/', '\\', $linkPath);
                $windowsTargetPath = str_replace('/', '\\', $targetPath);

                // Create directory symlink using mklink command
                exec("mklink /D \"{$windowsLinkPath}\" \"{$windowsTargetPath}\"");
            } else {
                // On Linux/Unix systems
                symlink($targetPath, $linkPath);
            }
        }
    }

    /**
     * Regenerate a new join URL for the webinar.
     */
    public function regenerateJoinUrl(Webinar $webinar)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        $this->authorize('update', $webinar);

        try {
            // Generate a new unique join code
            $joinCode = Webinar::generateJoinCode();
            $joinUrl = url("/join/{$joinCode}");

            // Update the webinar with the new join code and URL
            $webinar->update([
                'join_code' => $joinCode,
                'join_url' => $joinUrl,
            ]);

            return response()->json([
                'success' => true,
                'join_url' => $joinUrl,
                'message' => 'Đã tạo đường dẫn tham gia mới thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle chunked upload for S3 storage
     */
    public function chunkedUploadS3(Request $request, Webinar $webinar)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            $request->validate([
                'file' => 'required',
                'name' => 'required',
                'chunk' => 'required|numeric',
                'chunks' => 'required|numeric'
            ]);

            $originalFilename = $request->input('name');
            $chunk = (int)$request->input('chunk');
            $chunks = (int)$request->input('chunks');
            $videoDuration = $request->input('video_duration');

            // Generate a unique filename for this upload
            $fileId = $request->input('file_id', Str::random(20));
            $fileName = $fileId . '_' . pathinfo($originalFilename, PATHINFO_FILENAME) . '.mp4';

            // Setup temp directory with unique path for this webinar
            $tempDirectory = storage_path('app/temp/' . $webinar->id);
            if (!file_exists($tempDirectory)) {
                mkdir($tempDirectory, 0777, true);
            }

            $filePath = $tempDirectory . '/' . $fileName . '.part';

            Log::info('Processing chunk', [
                'webinar_id' => $webinar->id,
                'chunk' => $chunk,
                'chunks' => $chunks,
                'file_name' => $fileName,
                'video_duration' => $videoDuration
            ]);

            // Save chunk to temporary file
            file_put_contents(
                $filePath,
                $request->file('file')->get(),
                $chunk == 0 ? LOCK_EX : FILE_APPEND | LOCK_EX
            );

            // If this is the last chunk, complete the file and upload to S3
            if ($chunk == $chunks - 1) {
                $finalFilePath = $tempDirectory . '/' . $fileName;
                rename($filePath, $finalFilePath);

                Log::info('Upload complete, processing file', [
                    'webinar_id' => $webinar->id,
                    'file_name' => $fileName,
                    'file_size' => filesize($finalFilePath)
                ]);

                try {
                    // First upload to S3
                    $s3Key = 'webinar-' . $webinar->id . '/' . $fileName;

                    // Clean up any existing S3 files for this webinar to avoid duplication
                    $this->cleanupExistingS3Files($webinar);

                    // Upload to S3
                    $fileStream = fopen($finalFilePath, 'r');
                    $uploaded = Storage::disk('vns3')->put(
                        $s3Key,
                        $fileStream
                    );
                    fclose($fileStream);

                    if (!$uploaded) {
                        throw new \Exception("Failed to upload file to S3");
                    }

                    // Generate S3 URL
                    $s3BaseUrl = rtrim(config('filesystems.disks.vns3.url'), '/');
                    $bucket = config('filesystems.disks.vns3.bucket');
                    $s3Url = $s3BaseUrl . '/' . $bucket . '/' . $s3Key;

                    Log::info('S3 upload successful', [
                        'webinar_id' => $webinar->id,
                        's3_key' => $s3Key,
                        's3_url' => $s3Url
                    ]);
                } catch (\Exception $e) {
                    Log::error('S3 upload error: ' . $e->getMessage(), [
                        'webinar_id' => $webinar->id,
                        'trace' => $e->getTraceAsString()
                    ]);
                }

                // After S3 upload, also store in local public directory
                $publicDir = storage_path('app/public');
                $webinarDir = 'webinar/' . $webinar->id;
                $publicWebinarDir = $publicDir . '/' . $webinarDir;

                if (!file_exists($publicDir)) {
                    mkdir($publicDir, 0777, true);
                }

                if (!file_exists($publicWebinarDir)) {
                    mkdir($publicWebinarDir, 0777, true);
                }

                // Copy the file to public storage (don't move, make a copy)
                $publicFilePath = $publicWebinarDir . '/' . $fileName;
                copy($finalFilePath, $publicFilePath);

                Log::info('File stored in public storage', [
                    'webinar_id' => $webinar->id,
                    'file_name' => $fileName,
                    'public_path' => 'public/' . $webinarDir . '/' . $fileName
                ]);

                // Delete old video file if it exists
                if ($webinar->video_path) {
                    $oldVideoPath = storage_path('app/' . str_replace('public/', '', $webinar->video_path));
                    if (file_exists($oldVideoPath) && $oldVideoPath != $publicFilePath) {
                        @unlink($oldVideoPath);
                    }
                }

                // Get file size
                $fileSize = filesize($finalFilePath);

                // Update webinar with the new paths
                $webinar->update([
                    'video_path' => 'public/' . $webinarDir . '/' . $fileName,
                    's3_url' => $s3Url ?? null,
                    'video_duration_minutes' => $videoDuration ? (int)$videoDuration : null,
                    'video_file_size' => $fileSize
                ]);

                // Clean up temp file after copying to public
                @unlink($finalFilePath);

                // Ensure the storage symlink exists
                $this->ensurePublicSymlinkExists();

                return response()->json([
                    'success' => true,
                    'url' => $s3Url ?? url('storage/' . $webinarDir . '/' . $fileName),
                    'message' => 'Video đã được tải lên thành công',
                    'local_path' => 'public/' . $webinarDir . '/' . $fileName
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Đã tải lên phần ' . ($chunk + 1) . '/' . $chunks,
                'chunk' => $chunk,
                'file_id' => $fileId
            ]);
        } catch (\Exception $e) {
            Log::error('Chunked upload error: ' . $e->getMessage(), [
                'webinar_id' => $webinar->id,
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Lỗi khi tải lên: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle AJAX request to regenerate hash ID for a webinar
     */
    public function regenerateHashId(Request $request)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            $webinarId = $request->input('webinar_id');
            $webinar = Webinar::findOrFail($webinarId);

            $this->authorize('update', $webinar);

            // Generate new hash ID
            $hashId = $webinar->hashId();
            $url = route('webinars.show', $webinar);

            return response()->json([
                'success' => true,
                'webinar_id' => $webinar->id,
                'hash_id' => $hashId,
                'url' => $url,
                'message' => 'Đã tạo mới mã hash thành công!'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Đã xảy ra lỗi: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check if hash ID already exists
     */
    public function checkHashIdExists(Request $request)
    {
        if (!auth()->user()->can('webinars index')) {
            abort(403, 'Bạn không có quyền truy cập chức năng này.');
        }
        try {
            $hashId = $request->input('hash_id');
            $id = Webinar::decodeHashId($hashId);

            if (!$id) {
                return response()->json([
                    'success' => true,
                    'exists' => false,
                    'message' => 'Mã hash không tồn tại'
                ]);
            }

            $webinar = Webinar::find($id);

            return response()->json([
                'success' => true,
                'exists' => $webinar ? true : false,
                'webinar_id' => $webinar ? $webinar->id : null,
                'title' => $webinar ? $webinar->title : null
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lỗi kiểm tra mã hash: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show student management page
     */
    public function studentsManager(Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        return view('webinars.students', compact('webinar'));
    }

    /**
     * Search existing participants (students) by email or phone
     */
    public function searchUsers(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $query = $request->get('q');

        $participants = WebinarParticipant::where('webinar_id', $webinar->id)
            ->where('is_student', 0) // Only non-student participants
            ->where(function ($q) use ($query) {
                $q->where('email', 'like', "%{$query}%")
                    ->orWhere('phone', 'like', "%{$query}%")
                    ->orWhere('name', 'like', "%{$query}%");
            })
            ->limit(10)
            ->get();

        return response()->json([
            'success' => true,
            'users' => $participants
        ]);
    }

    /**
     * List students in webinar
     */
    public function listStudents(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $search = $request->get('search', '');
        $page = $request->get('page', 1);
        $perPage = 10;

        $query = WebinarParticipant::where('webinar_id', $webinar->id)
            ->where('is_student', 1) // Only students
            ->select([
                'id',
                'name',
                'email',
                'phone',
                'created_at as joined_at',
                'note'
            ]);

        if ($search) {
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $total = $query->count();
        $students = $query->offset(($page - 1) * $perPage)
            ->limit($perPage)
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $students,
            'pagination' => [
                'current_page' => (int)$page,
                'last_page' => ceil($total / $perPage),
                'total' => $total
            ]
        ]);
    }

    /**
     * Add existing participant as student to webinar
     */
    public function addStudent(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $request->validate([
            'user_id' => 'required|exists:webinar_participants,id'
        ]);

        // Find the participant
        $participant = WebinarParticipant::where('webinar_id', $webinar->id)
            ->where('id', $request->user_id)
            ->first();

        if (!$participant) {
            return response()->json([
                'success' => false,
                'message' => 'Không tìm thấy participant trong webinar này'
            ]);
        }

        // Check if already a student
        if ($participant->is_student) {
            return response()->json([
                'success' => false,
                'message' => 'Participant này đã là học sinh'
            ]);
        }

        // Convert participant to student
        $participant->update([
            'is_student' => 1,
            'note' => $request->note ?? $participant->note
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Đã chuyển participant thành học sinh thành công'
        ]);
    }

    /**
     * Create new student and add to webinar
     */
    public function createStudent(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:webinar_participants,email,NULL,id,webinar_id,' . $webinar->id,
            'phone' => 'required|string|max:20|unique:webinar_participants,phone,NULL,id,webinar_id,' . $webinar->id,
            'password' => 'required|string|min:6',
            'note' => 'nullable|string'
        ]);

        try {
            // Create student participant
            $student = WebinarParticipant::create([
                'webinar_id' => $webinar->id,
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'password' => Hash::make($request->password),
                'is_student' => 1,
                'note' => $request->note ?? '',
                'joined_at' => now(),
                'join_count' => 0
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Đã tạo học sinh mới và thêm vào lớp thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update student information
     */
    public function updateStudent(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $request->validate([
            'student_id' => 'required|exists:webinar_participants,id',
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:webinar_participants,email,' . $request->student_id . ',id,webinar_id,' . $webinar->id,
            'phone' => 'required|string|max:20|unique:webinar_participants,phone,' . $request->student_id . ',id,webinar_id,' . $webinar->id,
            'password' => 'nullable|string|min:6',
            'note' => 'nullable|string'
        ]);

        try {
            // Find the student
            $student = WebinarParticipant::where('webinar_id', $webinar->id)
                ->where('id', $request->student_id)
                ->where('is_student', 1)
                ->first();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy học sinh trong webinar này'
                ]);
            }

            // Update student information
            $updateData = [
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'note' => $request->note ?? ''
            ];

            if ($request->password) {
                $updateData['password'] = Hash::make($request->password);
            }

            $student->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Đã cập nhật thông tin học sinh thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove student from webinar (convert back to regular participant or delete)
     */
    public function removeStudent(Request $request, Webinar $webinar)
    {
        $this->authorize('update', $webinar);
        $request->validate([
            'student_id' => 'required|exists:webinar_participants,id'
        ]);

        try {
            // Find the student
            $student = WebinarParticipant::where('webinar_id', $webinar->id)
                ->where('id', $request->student_id)
                ->where('is_student', 1)
                ->first();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Không tìm thấy học sinh trong webinar này'
                ]);
            }

            // Option 1: Convert back to regular participant
            // $student->update(['is_student' => 0, 'password' => null]);

            // Option 2: Delete completely (we'll use this option)
            $student->delete();

            return response()->json([
                'success' => true,
                'message' => 'Đã xóa học sinh khỏi lớp thành công'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Có lỗi xảy ra: ' . $e->getMessage()
            ], 500);
        }
    }
}
